<?php

namespace App\Imports;

use App\Models\Transaction;
use App\Models\Titre;
use App\Models\Essence;
use App\Models\Societe;
use App\Models\Forme;
use App\Models\Type;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\SkipsEmptyRows;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\Importable;

class TransactionImport implements ToModel, SkipsEmptyRows
{
    use Importable;

    private $successCount = 0;
    private $errorCount = 0;
    private $errors = [];

    public function startRow(): int
    {
        return 1;
    }

    public function headingRow(): int
    {
        return 0;
    }

    public function model(array $row)
    {
        if (empty($row[0])) {
            return null;
        }

        try {
            DB::beginTransaction();

            // Conversion de la date Excel en format DateTime
            $date = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($row[0]);

            // Vérification de l'exportateur (société)
            $societe = Societe::find($row[3]);
            if (!$societe) {
                throw new \Exception("Exportateur non trouvé (ID: {$row[3]})");
            }

            // Vérification du titre
            $titre = Titre::find($row[6]);
            if (!$titre) {
                throw new \Exception("Titre non trouvé (ID: {$row[6]})");
            }

            // Vérification de l'essence
            $essence = Essence::find($row[7]);
            if (!$essence) {
                throw new \Exception("Essence non trouvée (ID: {$row[7]})");
            }

            // Vérification de la forme
            $forme = Forme::find($row[8]);
            if (!$forme) {
                throw new \Exception("Forme non trouvée (ID: {$row[8]})");
            }

            // Vérification du type
            $type = Type::find($row[10]);
            if (!$type) {
                throw new \Exception("Type non trouvé (ID: {$row[10]})");
            }

            // Vérification du volume disponible dans la relation titre-essence
            $volumeDisponible = $titre->essence()
                ->wherePivot('essence_id', $essence->id)
                ->first();

            if (!$volumeDisponible) {
                throw new \Exception("Aucune relation trouvée entre le titre et l'essence spécifiée");
            }

            // Créer la transaction
            $transaction = Transaction::create([
                'date' => $date,
                'exercice' => $row[1],
                'numero' => $row[2],
                'societe_id' => $societe->id,
                'destination' => strtoupper($row[4]),
                'pays' => strtoupper($row[5]),
                'titre_id' => $titre->id,
                'essence_id' => $essence->id,
                'forme_id' => $forme->id,
                'conditionnemment_id' => $row[9],
                'type_id' => $type->id,
                'volume' => $volumeDisponible->pivot->VolumeRestant
            ]);

            // Mettre à jour le volume restant
            $titre->essence()->updateExistingPivot($essence->id, [
                'VolumeRestant' => DB::raw("VolumeRestant - {$volumeDisponible->pivot->VolumeRestant}")
            ]);

            DB::commit();
            $this->successCount++;
            return $transaction;

        } catch (\Exception $e) {
            DB::rollBack();
            $this->errorCount++;
            $this->errors[] = [
                // 'ligne' => $this->getRowNumber(),
                'erreur' => $e->getMessage(),
                'donnees' => $row
            ];
            Log::error('Erreur importation transaction:', [
                'message' => $e->getMessage(),
                // 'ligne' => $this->getRowNumber(),
                'donnees' => $row
            ]);
            return null;
        }
    }

    public function getResultStats(): array
    {
        return [
            'success_count' => $this->successCount,
            'error_count' => $this->errorCount,
            'errors' => $this->errors
        ];
    }
}

