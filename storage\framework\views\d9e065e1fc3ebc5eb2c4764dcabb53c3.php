<nav style="color:#FFD700;" class="navbar navbar-expand-lg main-navbar">
    <style>
        .navbar .nav-link {
            color: #F8F9FA !important;
        }

        .dropdown-menu {
            border: 1px solid #FF6B35;
        }
    </style>
    <form class="form-inline mr-auto">
        <ul class="navbar-nav mr-3">
            <li><a href="#" data-toggle="sidebar" class="nav-link nav-link-lg"><i class="fas fa-bars"></i></a></li>
        </ul>
    </form>
    <ul class="navbar-nav navbar-right">

        <li class="dropdown">
            <a href="#" data-toggle="dropdown" class="nav-link dropdown-toggle nav-link-lg nav-link-user">

                    <img src="<?php echo e(Auth::user()->avatar ? Auth::user()->avatar : asset('back/img/avatar/avatar-1.png')); ?>"
                        alt="<?php echo e(Auth::user()->name); ?>" class="rounded-circle mr-1" width="30"
                        height="30">


                
                <div class="d-sm-none d-lg-inline-block"><?php echo e(auth()->user()->name); ?></div>
            </a>
            <div class="dropdown-menu dropdown-menu-right">

                <a href="<?php echo e(route('profile.edit')); ?>" class="dropdown-item has-icon">
                    <i class="far fa-user"></i> <?php echo app('translator')->get('Profile'); ?>
                </a>
                <div class="dropdown-divider"></div>
                <form method="POST" action="<?php echo e(route('logout')); ?>">
                    <?php echo csrf_field(); ?>
                    <a href="<?php echo e(route('logout')); ?>" onclick="event.preventDefault(); this.closest('form').submit();"
                        class="dropdown-item has-icon text-danger">
                        <i class="fas fa-sign-out-alt"></i> <?php echo app('translator')->get('Log Out'); ?>
                    </a>
                </form>
            </div>
        </li>
    </ul>
</nav>
<?php /**PATH H:\Laravel Projet\ComcamNewApplication\resources\views/includes/back/navbar.blade.php ENDPATH**/ ?>